import { AnswerRecord } from '../../types/database';
import { getDatabase } from './init';

export interface AnswerData {
  questionId: number;
  volume: number;
  chapter: number;
  isCorrect: boolean;
  mode: 'practice' | 'exam';
  sessionId: string;
  selectedOption: 'A' | 'B' | 'C' | 'D';
  correctOption: 'A' | 'B' | 'C' | 'D';
  timeSpent: number;
}

export async function recordAnswer(answer: AnswerData): Promise<void> {
  const db = getDatabase();
  
  try {
    await db.runAsync(
      `INSERT INTO answer_records 
       (question_id, volume, chapter, is_correct, mode, session_id, 
        selected_option, correct_option, time_spent) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        answer.questionId,
        answer.volume,
        answer.chapter,
        answer.isCorrect ? 1 : 0,
        answer.mode,
        answer.sessionId,
        answer.selectedOption,
        answer.correctOption,
        answer.timeSpent,
      ]
    );
    
    await updateQuestionStats(answer);
    await updateVolumeProgress(answer);
  } catch (error) {
    console.error('Failed to record answer:', error);
    throw error;
  }
}

async function updateQuestionStats(answer: AnswerData): Promise<void> {
  const db = getDatabase();
  
  try {
    // Try the new composite primary key approach first
    await db.runAsync(
      `INSERT INTO question_stats 
       (question_id, volume, total_attempts, correct_attempts, wrong_attempts, last_attempted)
       VALUES (?, ?, 1, ?, ?, datetime('now'))
       ON CONFLICT(question_id, volume) DO UPDATE SET
         total_attempts = total_attempts + 1,
         correct_attempts = correct_attempts + ?,
         wrong_attempts = wrong_attempts + ?,
         last_attempted = datetime('now')`,
      [
        answer.questionId,
        answer.volume,
        answer.isCorrect ? 1 : 0,
        answer.isCorrect ? 0 : 1,
        answer.isCorrect ? 1 : 0,
        answer.isCorrect ? 0 : 1,
      ]
    );
  } catch (error) {
    // If that fails, try the old single primary key approach
    console.log('Composite key failed, trying single key approach:', error);
    
    try {
      await db.runAsync(
        `INSERT INTO question_stats 
         (question_id, volume, total_attempts, correct_attempts, wrong_attempts, last_attempted)
         VALUES (?, ?, 1, ?, ?, datetime('now'))
         ON CONFLICT(question_id) DO UPDATE SET
           volume = ?,
           total_attempts = total_attempts + 1,
           correct_attempts = correct_attempts + ?,
           wrong_attempts = wrong_attempts + ?,
           last_attempted = datetime('now')`,
        [
          answer.questionId,
          answer.volume,
          answer.isCorrect ? 1 : 0,
          answer.isCorrect ? 0 : 1,
          answer.volume, // Update volume in case of conflict
          answer.isCorrect ? 1 : 0,
          answer.isCorrect ? 0 : 1,
        ]
      );
    } catch (fallbackError) {
      console.error('Both approaches failed:', fallbackError);
      throw fallbackError;
    }
  }
}

async function updateVolumeProgress(answer: AnswerData): Promise<void> {
  const db = getDatabase();
  
  await db.runAsync(
    `UPDATE volume_progress 
     SET seen_count = (
       SELECT COUNT(DISTINCT question_id) 
       FROM answer_records 
       WHERE volume = ?
     ),
     correct_count = (
       SELECT COUNT(*) 
       FROM answer_records 
       WHERE volume = ? AND is_correct = 1
     ),
     wrong_count = (
       SELECT COUNT(*) 
       FROM answer_records 
       WHERE volume = ? AND is_correct = 0
     ),
     last_practice = datetime('now'),
     updated_at = datetime('now')
     WHERE volume = ?`,
    [answer.volume, answer.volume, answer.volume, answer.volume]
  );
}

export async function getAnswerHistory(sessionId: string): Promise<AnswerRecord[]> {
  const db = getDatabase();
  
  const result = await db.getAllAsync(
    `SELECT * FROM answer_records 
     WHERE session_id = ? 
     ORDER BY created_at ASC`,
    [sessionId]
  ) as AnswerRecord[];
  
  return result;
}