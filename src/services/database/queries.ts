import { QuestionStats, VolumeProgress } from '../../types/database';
import { getDatabase } from './init';

export async function getVolumeProgress(): Promise<VolumeProgress[]> {
  const db = getDatabase();

  try {
    const result = await db.getAllAsync(
      `SELECT * FROM volume_progress ORDER BY volume ASC`
    ) as VolumeProgress[];

    return result;
  } catch (error) {
    console.error('Failed to get volume progress:', error);
    throw error;
  }
}

export async function getSeenQuestionIds(volumes: number[]): Promise<Set<number>> {
  const db = getDatabase();

  try {
    const placeholders = volumes.map(() => '?').join(',');
    const result = await db.getAllAsync(
      `SELECT DISTINCT question_id FROM question_stats
       WHERE volume IN (${placeholders}) AND total_attempts > 0`,
      volumes
    ) as { question_id: number }[];

    return new Set(result.map(row => row.question_id));
  } catch (error) {
    console.error('Failed to get seen question IDs:', error);
    throw error;
  }
}

export async function getQuestionStats(questionId: number, volume: number): Promise<QuestionStats | null> {
  const db = getDatabase();
  
  try {
    // Try composite primary key approach first
    let result = await db.getFirstAsync(
      `SELECT * FROM question_stats WHERE question_id = ? AND volume = ?`,
      [questionId, volume]
    ) as QuestionStats | null;
    
    // If not found, try single key approach (for backward compatibility)
    if (!result) {
      result = await db.getFirstAsync(
        `SELECT * FROM question_stats WHERE question_id = ?`,
        [questionId]
      ) as QuestionStats | null;
    }
    
    return result;
  } catch (error) {
    console.error('Failed to get question stats:', error);
    throw error;
  }
}

export async function getWrongQuestions(volume?: number): Promise<QuestionStats[]> {
  const db = getDatabase();
  
  try {
    const query = volume
      ? `SELECT * FROM question_stats 
         WHERE volume = ? AND wrong_attempts > 0 
         ORDER BY last_attempted DESC`
      : `SELECT * FROM question_stats 
         WHERE wrong_attempts > 0 
         ORDER BY last_attempted DESC`;
    
    const params = volume ? [volume] : [];
    
    const result = await db.getAllAsync(query, params) as QuestionStats[];
    
    return result;
  } catch (error) {
    console.error('Failed to get wrong questions:', error);
    throw error;
  }
}

export async function getBookmarkedQuestions(volume?: number): Promise<QuestionStats[]> {
  const db = getDatabase();
  
  try {
    const query = volume
      ? `SELECT * FROM question_stats 
         WHERE volume = ? AND is_bookmarked = 1 
         ORDER BY last_attempted DESC`
      : `SELECT * FROM question_stats 
         WHERE is_bookmarked = 1 
         ORDER BY last_attempted DESC`;
    
    const params = volume ? [volume] : [];
    
    const result = await db.getAllAsync(query, params) as QuestionStats[];
    
    return result;
  } catch (error) {
    console.error('Failed to get bookmarked questions:', error);
    throw error;
  }
}

export async function toggleBookmark(questionId: number, volume: number): Promise<void> {
  const db = getDatabase();
  
  try {
    // Try composite primary key approach first
    const result = await db.runAsync(
      `UPDATE question_stats 
       SET is_bookmarked = CASE 
         WHEN is_bookmarked = 1 THEN 0 
         ELSE 1 
       END 
       WHERE question_id = ? AND volume = ?`,
      [questionId, volume]
    );
    
    // If no rows affected, try single key approach
    if (result.changes === 0) {
      await db.runAsync(
        `UPDATE question_stats 
         SET is_bookmarked = CASE 
           WHEN is_bookmarked = 1 THEN 0 
           ELSE 1 
         END,
         volume = ?
         WHERE question_id = ?`,
        [volume, questionId]
      );
    }
  } catch (error) {
    console.error('Failed to toggle bookmark:', error);
    throw error;
  }
}

export async function updateQuestionNote(
  questionId: number,
  volume: number,
  note: string
): Promise<void> {
  const db = getDatabase();
  
  try {
    // Try composite primary key approach first
    await db.runAsync(
      `INSERT INTO question_stats (question_id, volume, note)
       VALUES (?, ?, ?)
       ON CONFLICT(question_id, volume) DO UPDATE SET
       note = ?`,
      [questionId, volume, note, note]
    );
  } catch (error) {
    // If that fails, try single key approach
    console.log('Composite key note update failed, trying single key approach:', error);
    
    try {
      await db.runAsync(
        `INSERT INTO question_stats (question_id, volume, note)
         VALUES (?, ?, ?)
         ON CONFLICT(question_id) DO UPDATE SET
         volume = ?, note = ?`,
        [questionId, volume, note, volume, note]
      );
    } catch (fallbackError) {
      console.error('Failed to update question note:', fallbackError);
      throw fallbackError;
    }
  }
}

export async function getWrongQuestionIds(volumes: number[]): Promise<Set<number>> {
  const db = getDatabase();
  
  try {
    const placeholders = volumes.map(() => '?').join(',');
    const result = await db.getAllAsync(
      `SELECT DISTINCT question_id FROM question_stats
       WHERE volume IN (${placeholders}) AND wrong_attempts > correct_attempts AND total_attempts > 0`,
      volumes
    ) as { question_id: number }[];
    
    return new Set(result.map(row => row.question_id));
  } catch (error) {
    console.error('Failed to get wrong question IDs:', error);
    throw error;
  }
}

export async function getBookmarkedQuestionIds(volumes: number[]): Promise<Set<number>> {
  const db = getDatabase();
  
  try {
    const placeholders = volumes.map(() => '?').join(',');
    const result = await db.getAllAsync(
      `SELECT DISTINCT question_id FROM question_stats
       WHERE volume IN (${placeholders}) AND is_bookmarked = 1`,
      volumes
    ) as { question_id: number }[];
    
    return new Set(result.map(row => row.question_id));
  } catch (error) {
    console.error('Failed to get bookmarked question IDs:', error);
    throw error;
  }
}