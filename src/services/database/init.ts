import * as SQLite from 'expo-sqlite';
import { CREATE_INDEXES, CREATE_TABLES } from './schema';

let database: SQLite.SQLiteDatabase | null = null;

export function getDatabase(): SQLite.SQLiteDatabase {
  if (!database) {
    throw new Error('Database not initialized. Call initializeDatabase() first.');
  }
  return database;
}

export async function initializeDatabase(): Promise<void> {
  try {
    database = await SQLite.openDatabaseAsync('macau_driving_test.db');
    
    // Enable WAL mode for better concurrent access
    await database.execAsync('PRAGMA journal_mode = WAL;');
    await database.execAsync('PRAGMA foreign_keys = ON;');
    
    // Try to migrate the question_stats table (non-blocking)
    try {
      await migrateQuestionStatsTable();
    } catch (migrationError) {
      console.warn('Migration failed, continuing with existing schema:', migrationError);
      // App will continue to work with the existing schema
    }
    
    // Create tables and indexes
    await database.execAsync(CREATE_TABLES);
    await database.execAsync(CREATE_INDEXES);
    
    // Initialize volume progress
    await initializeVolumeProgress(database);
    
    // console.log('Database initialized successfully');
  } catch (error) {
    console.error('Failed to initialize database:', error);
    throw error;
  }
}

/**
 * Reset database completely (for debugging purposes)
 * WARNING: This will delete all user data!
 */
export async function resetDatabase(): Promise<void> {
  try {
    if (database) {
      await database.closeAsync();
      database = null;
    }
    
    // Delete the database file and reinitialize
    const db = await SQLite.openDatabaseAsync('macau_driving_test.db');
    await db.execAsync('DROP TABLE IF EXISTS question_stats;');
    await db.execAsync('DROP TABLE IF EXISTS question_stats_backup;');
    await db.execAsync('DROP TABLE IF EXISTS answer_records;');
    await db.execAsync('DROP TABLE IF EXISTS sessions;');
    await db.execAsync('DROP TABLE IF EXISTS volume_progress;');
    await db.closeAsync();
    
    console.log('Database reset completed');
    
    // Reinitialize
    await initializeDatabase();
  } catch (error) {
    console.error('Failed to reset database:', error);
    throw error;
  }
}

/**
 * Migrate question_stats table from single primary key to composite primary key
 */
async function migrateQuestionStatsTable(): Promise<void> {
  try {
    // Check if the table exists and get its schema
    const tableInfo = await database!.getAllAsync(
      `PRAGMA table_info(question_stats)`
    ) as any[];
    
    if (tableInfo.length === 0) {
      console.log('question_stats table does not exist yet, no migration needed');
      return;
    }
    
    // Check if we already have composite primary key
    const primaryKeys = tableInfo.filter(col => col.pk > 0);
    if (primaryKeys.length > 1) {
      // console.log('question_stats table already has composite primary key, no migration needed');
      return;
    }
    
    console.log('Attempting question_stats table migration...');
    
    // Use a single transaction for the entire migration with timeout
    await Promise.race([
      database!.withTransactionAsync(async () => {
        // Step 1: Create backup table
        await database!.execAsync(`
          CREATE TABLE question_stats_backup AS 
          SELECT * FROM question_stats;
        `);
        
        // Step 2: Drop the old table
        await database!.execAsync('DROP TABLE question_stats;');
        
        // Step 3: Create new table with composite primary key
        await database!.execAsync(`
          CREATE TABLE question_stats (
            question_id INTEGER NOT NULL,
            volume INTEGER NOT NULL,
            total_attempts INTEGER DEFAULT 0,
            correct_attempts INTEGER DEFAULT 0,
            wrong_attempts INTEGER DEFAULT 0,
            last_attempted DATETIME,
            is_bookmarked BOOLEAN DEFAULT 0,
            note TEXT,
            PRIMARY KEY (question_id, volume)
          );
        `);
        
        // Step 4: Insert data back, handling potential duplicates
        await database!.execAsync(`
          INSERT OR REPLACE INTO question_stats 
          (question_id, volume, total_attempts, correct_attempts, wrong_attempts, last_attempted, is_bookmarked, note)
          SELECT 
            question_id, 
            volume,
            SUM(total_attempts) as total_attempts,
            SUM(correct_attempts) as correct_attempts, 
            SUM(wrong_attempts) as wrong_attempts,
            MAX(last_attempted) as last_attempted,
            MAX(is_bookmarked) as is_bookmarked,
            MAX(note) as note
          FROM question_stats_backup 
          GROUP BY question_id, volume;
        `);
        
        // Step 5: Drop backup table
        await database!.execAsync('DROP TABLE question_stats_backup;');
      }),
      // Timeout after 10 seconds
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Migration timeout')), 10000)
      )
    ]);
    
    console.log('question_stats table migration completed successfully');
    
  } catch (error) {
    console.error('Migration failed:', error);
    
    // Try to restore from backup if it exists
    try {
      const backupExists = await database!.getFirstAsync(
        `SELECT name FROM sqlite_master WHERE type='table' AND name='question_stats_backup'`
      );
      
      if (backupExists) {
        console.log('Restoring from backup...');
        await database!.execAsync('DROP TABLE IF EXISTS question_stats;');
        await database!.execAsync('ALTER TABLE question_stats_backup RENAME TO question_stats;');
        console.log('Restored from backup successfully');
      }
    } catch (restoreError) {
      console.warn('Could not restore from backup, will continue with existing schema');
    }
    
    // Re-throw the error to be caught by the caller
    throw error;
  }
}

async function initializeVolumeProgress(db: SQLite.SQLiteDatabase) {
  for (let volume = 1; volume <= 5; volume++) {
    await db.runAsync(
      `INSERT OR IGNORE INTO volume_progress (volume, total_questions) VALUES (?, ?)`,
      [volume, 0]
    );
  }
}