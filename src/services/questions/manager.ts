import shuffle from 'lodash.shuffle';
import { ProcessedQuestion } from '../../types/question';
import { PracticeConfig } from '../../types/session';
import { QuestionLoader } from './loader';

export class QuestionManager {
  static async generatePracticeQuestions(config: PracticeConfig): Promise<ProcessedQuestion[]> {
    try {
      let questions: ProcessedQuestion[] = [];

      // Load questions from selected volumes
      for (const volume of config.volumes) {
        const volumeQuestions = await QuestionLoader.loadVolume(volume);
        questions.push(...volumeQuestions);
      }

      // TODO: Filter by chapter if needed in the future
      // Chapter filtering not implemented yet as questions don't have chapter property

      // Filter by unseen questions if includeUnse<PERSON> is true
      if (config.includeUnseen) {
        try {
          const { getSeenQuestionIds } = await import('../database/queries');
          const seenQuestionIds = await getSeenQuestionIds(config.volumes);
          questions = questions.filter(q => !seenQuestionIds.has(q.id));
        } catch (error) {
          console.warn('Failed to filter unseen questions, showing all questions:', error);
          // Continue with all questions if filtering fails
        }
      }

      // Include wrong questions if specified
      if (config.includeWrongQuestions) {
        try {
          const { getWrongQuestionIds } = await import('../database/queries');
          const wrongQuestionIds = await getWrongQuestionIds(config.volumes);
          const wrongQuestions = questions.filter(q => wrongQuestionIds.has(q.id));
          
          // If we have wrong questions, prioritize them
          if (wrongQuestions.length > 0) {
            const normalQuestions = questions.filter(q => !wrongQuestionIds.has(q.id));
            // Mix wrong questions with normal ones (wrong questions appear more frequently)
            questions = [...wrongQuestions, ...wrongQuestions, ...normalQuestions];
          }
        } catch (error) {
          console.warn('Failed to load wrong questions:', error);
        }
      }

      // Include bookmarked questions if specified
      if (config.includeBookmarked) {
        try {
          const { getBookmarkedQuestionIds } = await import('../database/queries');
          const bookmarkedQuestionIds = await getBookmarkedQuestionIds(config.volumes);
          questions = questions.filter(q => bookmarkedQuestionIds.has(q.id));
        } catch (error) {
          console.warn('Failed to load bookmarked questions:', error);
        }
      }

      // Shuffle if random mode
      if (config.mode === 'random') {
        questions = shuffle(questions);
      }

      // For continuous practice mode, return all available questions
      // Users can practice indefinitely until they choose to leave
      return questions;
    } catch (error) {
      console.error('Failed to generate practice questions:', error);
      throw error;
    }
  }

  static async generateExamQuestions(): Promise<ProcessedQuestion[]> {
    try {
      const examQuestions: ProcessedQuestion[] = [];
      
      // Load questions from each volume according to exam rules
      const volumeDistribution = {
        1: 16, // 40%
        2: 8,  // 20%
        3: 8,  // 20%
        4: 8,  // 20% (combined from 4 and future 5)
      };

      for (const [volume, count] of Object.entries(volumeDistribution)) {
        const volumeNumber = parseInt(volume);
        const volumeQuestions = await QuestionLoader.loadVolume(volumeNumber);
        
        // Randomly select required number of questions from this volume
        const shuffledQuestions = shuffle(volumeQuestions);
        const selectedQuestions = shuffledQuestions.slice(0, count);
        
        examQuestions.push(...selectedQuestions);
      }

      // Final shuffle to mix all volumes
      return shuffle(examQuestions);
    } catch (error) {
      console.error('Failed to generate exam questions:', error);
      throw error;
    }
  }

  static validateQuestionSet(questions: ProcessedQuestion[]): boolean {
    if (questions.length === 0) {
      return false;
    }

    // Check each question has required fields
    return questions.every(q => 
      q.id &&
      q.volume &&
      q.question &&
      q.options &&
      Array.isArray(q.options) &&
      q.options.length === 4 &&
      q.options.some(opt => opt.isCorrect) // At least one correct answer
    );
  }

  static getQuestionsByVolume(questions: ProcessedQuestion[]): Record<number, ProcessedQuestion[]> {
    const grouped: Record<number, ProcessedQuestion[]> = {};
    
    questions.forEach(question => {
      if (!grouped[question.volume]) {
        grouped[question.volume] = [];
      }
      grouped[question.volume].push(question);
    });
    
    return grouped;
  }

  static getQuestionsByTags(questions: ProcessedQuestion[], volume?: number): Record<string, ProcessedQuestion[]> {
    let filteredQuestions = questions;
    
    if (volume) {
      filteredQuestions = questions.filter(q => q.volume === volume);
    }

    const grouped: Record<string, ProcessedQuestion[]> = {};
    
    filteredQuestions.forEach(question => {
      const tags = question.tags || ['未分類'];
      tags.forEach(tag => {
        const key = `${question.volume}-${tag}`;
        if (!grouped[key]) {
          grouped[key] = [];
        }
        grouped[key].push(question);
      });
    });
    
    return grouped;
  }
}