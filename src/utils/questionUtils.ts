import { ProcessedQuestion } from '../types/question';

/**
 * Shuffles an array using Fisher<PERSON><PERSON> algorithm
 */
export function shuffleArray<T>(array: T[]): T[] {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
}

/**
 * Creates a shuffled version of question options with mapping information
 */
export function createShuffledOptions(options: ProcessedQuestion['options']) {
  // Create array of options with their original indices
  const optionsWithIndices = options.map((option, index) => ({
    ...option,
    originalIndex: index,
  }));

  // Shuffle the options
  const shuffledOptions = shuffleArray(optionsWithIndices);

  // Create mapping from new index to original index
  const indexMapping = shuffledOptions.map(option => option.originalIndex);

  // Create mapping from original index to new index  
  const reverseIndexMapping = new Map<number, number>();
  indexMapping.forEach((originalIndex, newIndex) => {
    reverseIndexMapping.set(originalIndex, newIndex);
  });

  return {
    shuffledOptions: shuffledOptions.map(({ originalIndex, ...option }) => option),
    mapOriginalToShuffled: (originalIndex: number) => reverseIndexMapping.get(originalIndex) ?? originalIndex,
    mapShuffledToOriginal: (shuffledIndex: number) => indexMapping[shuffledIndex] ?? shuffledIndex,
  };
} 