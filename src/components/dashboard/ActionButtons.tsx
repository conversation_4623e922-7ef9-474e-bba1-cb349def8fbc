import React from 'react';
import { StyleSheet, View } from 'react-native';
import { COLORS } from '../../utils/constants';
import { Button } from '../common';

interface ActionButtonsProps {
  onStartPractice: () => void;
  onStartExam: () => void;
  onStartWrongQuestionsReview: () => void;
}

export function ActionButtons({
  onStartPractice,
  onStartExam,
  onStartWrongQuestionsReview,
}: ActionButtonsProps) {
  return (
    <View style={styles.container}>
      {/* 開始考試按鈕 */}
      <Button
        title="開始模擬考試"
        onPress={onStartExam}
        style={styles.examButton}
        textStyle={styles.examButtonText}
      />

      {/* 練習按鈕 */}
      <Button
        title="進入練習模式"
        onPress={onStartPractice}
        variant="outline"
        style={styles.practiceButton}
        textStyle={styles.practiceButtonText}
      />

      {/* 錯題回顧按鈕 */}
      <Button
        title="錯題回顧"
        onPress={onStartWrongQuestionsReview}
        variant="outline"
        style={styles.reviewButton}
        textStyle={styles.reviewButtonText}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 24,
    gap: 14,
  },
  examButton: {
    backgroundColor: COLORS.ACCENT,
    borderRadius: 20,
    borderColor: COLORS.ACCENT,
    paddingVertical: 18,
    paddingHorizontal: 24,
    shadowColor: COLORS.ACCENT,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 6,
  },
  examButtonText: {
    color: COLORS.CARD_BACKGROUND,
    fontSize: 17,
    fontWeight: '700',
    textAlign: 'center',
    letterSpacing: 0.5,
  },
  practiceButton: {
    borderColor: COLORS.CARD_BACKGROUND,
    borderRadius: 20,
    paddingVertical: 16,
    paddingHorizontal: 24,
    backgroundColor: COLORS.CARD_BACKGROUND,
    shadowColor: COLORS.SHADOW,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,
  },
  practiceButtonText: {
    color: COLORS.ACCENT,
    fontSize: 17,
    fontWeight: '600',
    textAlign: 'center',
    letterSpacing: 0.3,
  },
  reviewButton: {
    borderWidth: 0,
    borderRadius: 20,
    paddingVertical: 16,
    paddingHorizontal: 24,
    backgroundColor: COLORS.WARNING,
  },
  reviewButtonText: {
    color: COLORS.CARD_BACKGROUND,
    fontSize: 17,
    fontWeight: '600',
    textAlign: 'center',
    letterSpacing: 0.3,
  },
});