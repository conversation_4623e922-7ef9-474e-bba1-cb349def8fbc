import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import Svg, { Circle } from 'react-native-svg';
import { OverallStats, PredictedPassRate } from '../../types/statistics';
import { COLORS } from '../../utils/constants';
import { Card } from '../common';

interface TopStatusDashboardProps {
  stats: OverallStats;
  prediction: PredictedPassRate;
  userName?: string;
}

export function TopStatusDashboard({ stats, prediction }: TopStatusDashboardProps) {

  const getColorByPercentage = (percentage: number) => {
    if (percentage >= 80) return COLORS.SUCCESS;
    if (percentage >= 60) return COLORS.WARNING;
    return COLORS.ERROR;
  };

  const masteredQuestions = stats.volumeStats.reduce((total, volume) => total + volume.correctAnswers, 0);
  const totalQuestions = stats.volumeStats.reduce((total, volume) => total + volume.totalQuestions, 0);

  // Circle progress calculations
  const size = 100;
  const strokeWidth = 8;
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (prediction.percentage / 100) * circumference;

  return (
    <Card style={styles.container}>
      <View style={styles.content}>
        {/* 左側統計數據 */}
        <View style={styles.leftSection}>
          <View style={styles.dataItem}>
            <Text style={styles.dataValue}>{masteredQuestions} / {totalQuestions}</Text>
            <Text style={styles.dataLabel}>已掌握題目</Text>
          </View>
          <View style={styles.dataItem}>
            <Text style={styles.dataValue}>{stats.accuracy.toFixed(1)}%</Text>
            <Text style={styles.dataLabel}>總正確率</Text>
          </View>
        </View>

        {/* 右側圓形圖表 */}
        <View style={styles.rightSection}>
          <View style={styles.circularProgress}>
            <Svg width={size} height={size}>
              {/* Background circle */}
              <Circle
                cx={size / 2}
                cy={size / 2}
                r={radius}
                stroke={COLORS.BORDER}
                strokeWidth={strokeWidth}
                fill="transparent"
              />
              {/* Progress circle */}
              <Circle
                cx={size / 2}
                cy={size / 2}
                r={radius}
                stroke={getColorByPercentage(prediction.percentage)}
                strokeWidth={strokeWidth}
                fill="transparent"
                strokeDasharray={strokeDasharray}
                strokeDashoffset={strokeDashoffset}
                strokeLinecap="round"
                transform={`rotate(-90 ${size / 2} ${size / 2})`}
              />
            </Svg>
            <View style={styles.circleContent}>
              <Text style={[styles.percentageText, { color: getColorByPercentage(prediction.percentage) }]}>
                {prediction.percentage}%
              </Text>
              <Text style={styles.metricLabel}>預測合格率</Text>
            </View>
          </View>
        </View>
      </View>
    </Card>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
    backgroundColor: COLORS.CARD_BACKGROUND,
    borderRadius: 20,
    paddingVertical: 24,
    paddingHorizontal: 24,
    shadowColor: COLORS.SHADOW,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 5,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  leftSection: {
    flex: 1,
    paddingRight: 20,
  },
  rightSection: {
    alignItems: 'center',
  },
  circularProgress: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 100,
    height: 100,
    position: 'relative',
  },
  circleContent: {
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
    width: 100,
    height: 100,
  },
  percentageText: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  metricLabel: {
    fontSize: 11,
    color: COLORS.TEXT_LIGHT,
    fontWeight: '600',
    textAlign: 'center',
    lineHeight: 12,
  },
  dataItem: {
    marginBottom: 16,
  },
  dataValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: COLORS.TEXT,
    marginBottom: 4,
  },
  dataLabel: {
    fontSize: 13,
    color: COLORS.TEXT_LIGHT,
    fontWeight: '500',
  },
});
