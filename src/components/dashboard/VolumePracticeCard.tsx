import { useRouter } from 'expo-router';
import React, { useState } from 'react';
import { ImageBackground, Pressable, StyleSheet, Text, View, Alert } from 'react-native';
import { COLORS } from '../../utils/constants';
import { QuestionManager } from '../../services/questions/manager';
import { usePracticeStore } from '../../store/usePracticeStore';
import { PracticeConfig } from '../../types/session';

type VolumeStatsDisplay = {
  volume: number;
  title: string;
  correct: number;
  wrong: number;
  unseen: number;
  total: number;
  image: string;
};

type VolumePracticeCardProps = {
  volumeStats: VolumeStatsDisplay;
};

const VolumePracticeCard: React.FC<VolumePracticeCardProps> = ({ volumeStats }) => {
  const router = useRouter();
  const { startSession } = usePracticeStore();
  const [loading, setLoading] = useState(false);

  const handlePress = async () => {
    setLoading(true);
    
    try {
      const config: PracticeConfig = {
        volumes: [volumeStats.volume],
        chapter: null,
        mode: 'sequential',
        includeWrongQuestions: false,
        includeBookmarked: false,
        includeUnseen: true,
      };

      const questions = await QuestionManager.generatePracticeQuestions(config);
      
      if (questions.length === 0) {
        Alert.alert('沒有題目', '此冊別內沒有找到題目，請稍後再試。');
        return;
      }

      startSession(config, questions);
      router.push('/practice/session');
    } catch (error) {
      console.error('Failed to start volume practice:', error);
      Alert.alert('錯誤', '無法載入練習題目，請稍後再試。');
    } finally {
      setLoading(false);
    }
  };

  const progressData = [
    { value: volumeStats.correct, color: COLORS.SUCCESS }, // Green for correct
    { value: volumeStats.wrong, color: COLORS.ERROR },     // Red for wrong
    { value: volumeStats.unseen, color: COLORS.BORDER },   // Gray for unseen
  ];

  return (
    <Pressable onPress={handlePress} disabled={loading}>
      <ImageBackground
        source={{ uri: volumeStats.image }}
        style={[styles.card, loading && styles.cardLoading]}
        imageStyle={styles.imageStyle}
      >
        <View style={styles.overlay}>
          <View style={styles.contentContainer}>
            <Text style={styles.subHeader}>第 {volumeStats.volume} 冊</Text>
            <Text style={styles.title}>{volumeStats.title}</Text>
            <View style={styles.progressBarContainer}>
              {progressData.map((segment, index) => {
                const segmentWidth = (segment.value / volumeStats.total) * 100;
                if (segmentWidth === 0) return null;
                return (
                  <View
                    key={index}
                    style={{
                      width: `${segmentWidth}%`,
                      height: '100%',
                      backgroundColor: segment.color,
                    }}
                  />
                );
              })}
            </View>
          </View>
        </View>
      </ImageBackground>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  card: {
    height: 200,
    borderRadius: 20,
    overflow: 'hidden',
    justifyContent: 'flex-end',
    shadowColor: COLORS.SHADOW,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 5,
    marginBottom: 4,
  },
  cardLoading: {
    opacity: 0.7,
  },
  imageStyle: {
    borderRadius: 20,
  },
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    padding: 18,
    justifyContent: 'flex-end',
    borderRadius: 20,
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  subHeader: {
    color: COLORS.CARD_BACKGROUND,
    fontSize: 13,
    fontWeight: '700',
    opacity: 0.9,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  title: {
    color: COLORS.CARD_BACKGROUND,
    fontSize: 17,
    fontWeight: 'bold',
    marginTop: 6,
    lineHeight: 20,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  progressBarContainer: {
    height: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 4,
    overflow: 'hidden',
    flexDirection: 'row',
    marginTop: 12,
    shadowColor: 'rgba(0, 0, 0, 0.2)',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.5,
    shadowRadius: 2,
  },
});

export default VolumePracticeCard;
