import { setAlpha } from '@/src/utils/color';
import { COLORS } from '@/src/utils/constants';
import React, { useMemo } from 'react';
import {
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { ProcessedQuestion } from '../../types/question';
import { createShuffledOptions } from '../../utils/questionUtils';

interface OptionsListProps {
  options: ProcessedQuestion['options'];
  selectedOption?: number;
  onOptionSelect: (optionIndex: number) => void;
  showAnswer?: boolean;
  disabled?: boolean;
}

export function OptionsList({
  options,
  selectedOption,
  onOptionSelect,
  showAnswer = false,
  disabled = false,
}: OptionsListProps) {
  // Create shuffled options and mappings - memoize to keep consistent during re-renders
  const { shuffledOptions, mapOriginalToShuffled, mapShuffledToOriginal } = useMemo(
    () => createShuffledOptions(options),
    [JSON.stringify(options)] // Re-shuffle only when the actual options change
  );

  type Variant = 'default' | 'selected' | 'correct' | 'wrong';

  const resolveVariant = (shuffledIndex: number): Variant => {
    // Convert shuffled index back to original to check if it's the selected option
    const originalIndex = mapShuffledToOriginal(shuffledIndex);
    const isSelected = selectedOption === originalIndex;
    const isCorrect = !!shuffledOptions[shuffledIndex]?.isCorrect;
    
    if (showAnswer && isCorrect) return 'correct';
    if (showAnswer && isSelected && !isCorrect) return 'wrong';
    if (isSelected) return 'selected';
    return 'default';
  };

  const getStyles = (shuffledIndex: number) => {
    const variant = resolveVariant(shuffledIndex);
    return {
      option: [
        styles.option,
        variant === 'selected' && styles.selectedOption,
        variant === 'correct' && styles.correctOption,
        variant === 'wrong' && styles.wrongOption,
        disabled && styles.disabledOption,
      ],
      text: [
        styles.optionText,
        variant === 'selected' && styles.selectedOptionText,
        variant === 'correct' && styles.correctOptionText,
        variant === 'wrong' && styles.wrongOptionText,
      ],
      label: [
        styles.optionLabel,
        variant === 'selected' && styles.selectedOptionLabel,
        variant === 'correct' && styles.correctOptionLabel,
        variant === 'wrong' && styles.wrongOptionLabel,
      ],
      labelText: [
        styles.optionLabelText,
        variant === 'selected' && styles.selectedOptionLabelText,
        variant === 'correct' && styles.correctOptionLabelText,
        variant === 'wrong' && styles.wrongOptionLabelText,
      ],
    };
  };

  const renderOption = (option: { text: string; isCorrect: boolean }, shuffledIndex: number) => {
    const optionLabels = ['A', 'B', 'C', 'D'];
    
    const s = getStyles(shuffledIndex);
    
    const handleOptionPress = () => {
      if (!disabled) {
        // Convert shuffled index back to original index before calling onOptionSelect
        const originalIndex = mapShuffledToOriginal(shuffledIndex);
        onOptionSelect(originalIndex);
      }
    };
    
    return (
      <TouchableOpacity
        key={shuffledIndex}
        style={s.option as any}
        onPress={handleOptionPress}
        disabled={disabled}
        activeOpacity={0.8}
      >
        <View style={styles.optionContent}>
          <View style={s.label as any}>
            <Text style={s.labelText as any}>{optionLabels[shuffledIndex]}</Text>
          </View>
          <Text style={s.text as any}>
            {option.text}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      {shuffledOptions.map(renderOption)}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    gap: 12,
    paddingBottom: 16,
  },
  option: {
    borderRadius: 12,
    borderWidth: 2,
    borderColor: COLORS.BORDER,
    backgroundColor: '#ffffff',
    padding: 16,
  },
  selectedOption: {
    borderColor: COLORS.THEME,
    backgroundColor: COLORS.CARD_BACKGROUND,
  },
  correctOption: {
    borderColor: '#34C759',
    // backgroundColor: '#F0FFF4',
  },
  wrongOption: {
    borderColor: '#FF3B30',
    backgroundColor: '#FFF5F5',
  },
  disabledOption: {
    opacity: 0.8,
  },
  optionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  optionLabel: {
    width: 26,
    height: 26,
    borderRadius: 16,
    backgroundColor: '#F2F2F7',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
    borderColor: COLORS.BORDER,
    borderWidth: 1,
  },
  selectedOptionLabel: {
    borderColor: COLORS.THEME,
    backgroundColor: setAlpha(COLORS.THEME, 0.2),
  },
  correctOptionLabel: {
    borderColor: COLORS.SUCCESS,
    backgroundColor: setAlpha(COLORS.SUCCESS, 0.15),
  },
  wrongOptionLabel: {
    borderColor: COLORS.ERROR,
    backgroundColor: setAlpha(COLORS.ERROR, 0.15),
  },
  optionLabelText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1c1c1e',
  },
  selectedOptionLabelText: {
    color: COLORS.THEME_TEXT,
  },
  correctOptionLabelText: {
    color: COLORS.SUCCESS,
  },
  wrongOptionLabelText: {
    color: COLORS.ERROR,
  },
  optionText: {
    flex: 1,
    fontSize: 16,
    lineHeight: 20,
    color: '#1c1c1e',
  },
  selectedOptionText: {
    color: COLORS.TEXT,
    fontWeight: '500',
  },
  correctOptionText: {
    color: '#34C759',
    fontWeight: '500',
  },
  wrongOptionText: {
    color: '#FF3B30',
    fontWeight: '500',
  },
});