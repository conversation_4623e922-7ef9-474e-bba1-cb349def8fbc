import { COLORS } from '@/src/utils/constants';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { DarkTheme, DefaultTheme, ThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import { useEffect } from 'react';
import { useColorScheme } from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import 'react-native-reanimated';
import { initializeDatabase } from '../src/services/database';

export default function RootLayout() {
  const colorScheme = useColorScheme();
  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  useEffect(() => {
    // Initialize database when app starts
    initializeDatabase().catch(error => {
      console.error('Failed to initialize database on app start:', error);
    });
  }, []);

  if (!loaded) {
    return null;
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <ThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
        <BottomSheetModalProvider>
          <Stack screenOptions={{
            headerStyle: {
              backgroundColor: COLORS.BACKGROUND,
            },
            headerShadowVisible: false,
          }}>
            <Stack.Screen name="index" options={{
              title: '首頁',
              headerShown: false
            }} />
            <Stack.Screen 
              name="practice/options" 
              options={{ 
                title: '練習模式',
                headerShown: true,
                headerBackVisible: true,
                headerBackButtonDisplayMode: 'minimal',
                headerTintColor: 'black',
              }} 
            />
            <Stack.Screen 
              name="practice/session" 
              options={{ 
                title: '練習',
                headerShown: false, 
              }} 
            />
            <Stack.Screen 
              name="review/index" 
              options={{ 
                title: '錯題回顧',
                headerShown: true,
                headerBackVisible: true,
                headerBackButtonDisplayMode: 'minimal',
                headerTintColor: 'black',
              }} 
            />
            <Stack.Screen 
              name="review/session" 
              options={{ 
                title: '錯題回顧',
                headerShown: false, 
              }} 
            />
            <Stack.Screen name="+not-found" />
          </Stack>
        </BottomSheetModalProvider>
      </ThemeProvider>
    </GestureHandlerRootView>
  );
}
